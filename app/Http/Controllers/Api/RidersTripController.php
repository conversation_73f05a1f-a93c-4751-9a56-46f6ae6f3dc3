<?php

namespace App\Http\Controllers\Api;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\RiderGlobalStatus;
use App\Enums\Trips\CancellationStage;
use App\Enums\Trips\TripStatus;
use App\Enums\UserStatus;
use App\Events\TripEvents\CancellationLimitWarning;
use App\Events\TripEvents\CancelRideRequest;
use App\Events\TripEvents\EditTripRequest;
use App\Http\Controllers\Controller;
use App\Http\Resources\RiderFavoritesCollection;
use App\Http\Resources\TripCollection;
use App\Http\Resources\TripResource;
use App\Http\Responses\ApiResponse;
use App\Models\Address;
use App\Models\Trip;
use App\Models\TripCancellation;
use App\Models\TripLocation;
use App\Models\User;
use App\Notifications\Firebase_notifications\RiderCanceledRide;
use App\Services\AreaDetectionService;
use App\Services\DriverRequestService;
use App\Services\GoogleMapsService;
use App\Services\TripPricingService;
use App\Services\TripService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class RidersTripController extends Controller
{
    public function __construct()
    {
        $this->maxCancellations = (int) env('MAX_CANCELLATIONS', 3);
    }

    private int $maxCancellations;

    /**
     * @status 200
     *
     * @response TripCollection
     */
    public function tripList(Request $request)
    {
        $user = Auth::user();
        if (! $user->rider) {
            return ApiResponse::error('', 'Unauthorized access', 403);
        }
        $trips = $user->rider->trips()
            ->whereIn('status', [TripStatus::canceled->value, TripStatus::completed->value])
            ->paginate(20);

        foreach ($trips as $trip) {
            $trip['departure_label'] = null;
            $trip['arrival_label'] = null;

            // Check if tripLocation exists before accessing its properties
            if ($trip->tripLocation) {

                $departure_addresss = Address::where('addressable_id', $trip->rider->user->id)
                    ->where('longitude', $trip->tripLocation->departure_lng)
                    ->where('latitude', $trip->tripLocation->departure_lat)
                    ->first();

                $arrival_addresss = Address::where('addressable_id', $trip->rider->user->id)
                    ->where('longitude', $trip->tripLocation->arrival_lng)
                    ->where('latitude', $trip->tripLocation->arrival_lat)
                    ->first();

                if ($departure_addresss && $departure_addresss->label) {
                    $trip['departure_label'] = $departure_addresss->label->label ?? null;
                }
                if ($arrival_addresss && $arrival_addresss->label) {
                    $trip['arrival_label'] = $arrival_addresss->label->label ?? null;
                }
            }
        }

        return new ApiResponse(new TripCollection($trips), 'Trips retrieved successfully', 200);
    }

    /**
     * Trip detail
     *
     * @status 200
     *
     * @response TripResource
     */
    public function trip(Request $request, Trip $trip)
    {
        if (Auth::user()->rider->id != $trip->rider_id) {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        // Add departure and arrival labels
        $trip['departure_label'] = null;
        $trip['arrival_label'] = null;

        // Check if tripLocation exists before accessing its properties
        if ($trip->tripLocation) {
            $departure_addresss = Address::where('addressable_id', $trip->rider->user->id)
                ->where('longitude', $trip->tripLocation->departure_lng)
                ->where('latitude', $trip->tripLocation->departure_lat)
                ->first();

            $arrival_addresss = Address::where('addressable_id', $trip->rider->user->id)
                ->where('longitude', $trip->tripLocation->arrival_lng)
                ->where('latitude', $trip->tripLocation->arrival_lat)
                ->first();

            if ($departure_addresss && $departure_addresss->label) {
                $trip['departure_label'] = $departure_addresss->label->label ?? null;
            }
            if ($arrival_addresss && $arrival_addresss->label) {
                $trip['arrival_label'] = $arrival_addresss->label->label ?? null;
            }
        }

        if ($trip->driver && $trip->driver->trips()->count() >= 5) {
            $tripRating = $trip->tripRatings()->first();
            $trip['rating'] = $tripRating ? $tripRating->rider_to_driver_rating : null;
        }

        $trip['total_price'] = json_decode($trip->pricing_breakdown, true);
        $trip['total_price'] = $trip['total_price']['total'] ?? null;

        $data = $trip
            ->load(
                'driver.user',
                'vehicle.vehicleModel.vehicleBrand',
                'tripLocation',
                'vehicleType',
            );

        return ApiResponse::success(new TripResource($data), 'Rider ride retrieved successfully', 200);
    }

    /**
     * List favorite addresses

     *
     * @status 200
     *
     * @response array{ addresses : RiderFavoritesCollection}
     */
    public function favoriteAddresses()
    {
        $rider = Auth::user()->load('rider');

        if ($rider->type !== 'passenger') {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        $data = Address::where('addressable_type', User::class)
            ->where('addressable_id', Auth::id())
            ->where('is_favorite', true)
            ->paginate(20);

        return ApiResponse::success(new RiderFavoritesCollection($data), 'Rider favorite addresses retrieved successfully', 200);
    }

    /**
     * List favorite trips
     *
     * @status 200
     *
     * @response array{ trips : TripResource}
     */
    public function favoriteTrips()
    {
        $rider = Auth::user()->load('rider');

        if ($rider->type !== 'passenger') {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        $favoriteTrips = $rider->rider->trips()
            ->where('is_favorite', true)
            ->with(['tripLocation'])
            ->paginate(20);

        return ApiResponse::success(new RiderFavoritesCollection($favoriteTrips), 'Rider favorite addresses retrieved successfully', 200);
    }

    public function addToFavorite(Request $request)
    {
        $validated = $request->validate([
            'trip_id' => 'required|numeric',
            'favorite' => 'boolean',
        ]);

        $trip = Trip::find($validated['trip_id']);

        if (! $trip) {
            return ApiResponse::error(null, 'Trip not found', 404);
        }

        if ($trip->rider_id !== Auth::user()->rider->id) {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        if ($validated['favorite'] === false) {
            $trip->update(['is_favorite' => false]);

            return ApiResponse::success(null, 'Trip removed from favorite successfully', 200);
        }

        $trip->update(['is_favorite' => true]);

        return ApiResponse::success(null, 'Trip added to favorite successfully', 200);
    }

    private const NON_COUNTING_REASONS = [
        'lost_connection',
        'driver_delay',
        'info_mismatch',
        'no_show',
    ];

    public function cancelTrip(
        int $tripId,
        int $userId,
        string $cancelledBy,
        string $reason,
    ): array {
        Log::info('Starting trip cancellation process', [
            'trip_id' => $tripId,
            'user_id' => $userId,
            'cancelled_by' => $cancelledBy,
            'reason' => $reason,
        ]);

        $trip = Trip::where('id', $tripId)->first();
        $user = User::where('id', $userId)->first();

        if (! $trip || ! $user) {
            Log::error('Trip or user not found', [
                'trip_exists' => (bool) $trip,
                'user_exists' => (bool) $user,
                'trip_id' => $tripId,
                'user_id' => $userId,
            ]);
            throw new \Exception('Trip or user not found');
        }

        Log::info('Current trip status', [
            'trip_id' => $trip->id,
            'current_status' => $trip->status->value,
        ]);

        try {
            DB::beginTransaction();

            // Release driver locks if this is a dispatched trip
            if ($trip->status->value === TripStatus::dispatched->value) {
                $contactedDrivers = $trip->contacted_drivers ?? [];

                if (! empty($contactedDrivers)) {
                    Log::info('Releasing locks for contacted drivers', [
                        'trip_id' => $trip->id,
                        'driver_count' => count($contactedDrivers),
                    ]);

                    $driverRequestService = app(DriverRequestService::class);
                    foreach ($contactedDrivers as $driverId) {
                        $driverRequestService->releaseDriverLock($driverId);
                    }
                }
            }

            $countsTowardsLimit = ! in_array($reason, self::NON_COUNTING_REASONS);
            Log::info('Cancellation limit check', [
                'counts_towards_limit' => $countsTowardsLimit,
                'reason' => $reason,
            ]);

            $newStatus = $reason === 'no_show' && $trip->status->value === TripStatus::driver_arrived->value
                ? TripStatus::no_show
                : TripStatus::canceled;

            // Determine cancellation stage
            switch ($trip->status->value) {
                case TripStatus::dispatched->value:
                    $cancellationStage = CancellationStage::afterDispatch;
                    break;
                case TripStatus::assigned->value:
                case TripStatus::driver_arriving->value:
                    $cancellationStage = CancellationStage::afterAssigned;
                    break;
                case TripStatus::driver_arrived->value:
                    $cancellationStage = CancellationStage::onPickup;
                    break;
                case TripStatus::on_trip->value:
                    $cancellationStage = CancellationStage::onTrip;
                    break;
            }

            Log::info('Creating cancellation record', [
                'trip_id' => $tripId,
                'cancellation_stage' => $cancellationStage->value,
                'new_status' => $newStatus->value,
            ]);

            $cancellation = TripCancellation::create([
                'trip_id' => $tripId,
                'user_id' => $userId,
                'cancelled_by' => $cancelledBy,
                'reason' => $reason,
                'counts_towards_limit' => $countsTowardsLimit,
                'cancelled_at' => now(),
            ]);

            Log::info('Updating trip status', [
                'trip_id' => $trip->id,
                'old_status' => $trip->status->value,
                'new_status' => $newStatus->value,
                'cancellation_stage' => $cancellationStage->value,
            ]);

            $trip->update([
                'status' => $newStatus->value,
                'cancelled_by' => $cancelledBy,
                'cancellation_stage' => $cancellationStage->value,
            ]);

            $trip->rider->user->update(['status' => UserStatus::ONLINE->value]);
            $trip->driver->user->update(['status' => UserStatus::ONLINE->value]);

            Log::info('Broadcasting cancellation event', ['trip_id' => $trip->id]);
            broadcast(new CancelRideRequest($trip, $cancelledBy, $reason));

            if ($countsTowardsLimit) {
                Log::info('Processing cancellation limits', [
                    'user_id' => $user->id,
                    'user_type' => $cancelledBy,
                ]);
                $this->handleCancellationLimits($user, $cancelledBy, $trip);
            }

            DB::commit();
            Log::info('Trip cancellation completed successfully', ['trip_id' => $trip->id]);

            return [
                'success' => true,
                'message' => 'Trip cancelled successfully',
                'cancellation' => $cancellation,
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Trip cancellation failed', [
                'trip_id' => $tripId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    private function handleCancellationLimits(User $user, string $userType, $trip): void
    {
        $weekStart = Carbon::now()->startOfWeek(Carbon::SATURDAY);
        $weekEnd = clone $weekStart;
        $weekEnd->addDays(6)->endOfDay();

        Log::info('Checking cancellation limits', [
            'user_id' => $user->id,
            'user_type' => $userType,
            'week_start' => $weekStart->toDateTimeString(),
            'week_end' => $weekEnd->toDateTimeString(),
        ]);

        $cancellationsCount = TripCancellation::where('user_id', $user->id)
            ->where('cancelled_by', $userType)
            ->where('counts_towards_limit', true)
            ->whereBetween('cancelled_at', [$weekStart, $weekEnd])
            ->count();

        $remainingCancellations = $this->maxCancellations - $cancellationsCount;

        Log::info('Cancellation count status', [
            'user_id' => $user->id,
            'total_cancellations' => $cancellationsCount,
            'remaining_cancellations' => $remainingCancellations,
            'max_cancellations' => $this->maxCancellations,
        ]);

        if ($cancellationsCount > $this->maxCancellations) {
            Log::warning('User exceeded cancellation limit', [
                'user_id' => $user->id,
                'user_type' => $userType,
                'cancellations_count' => $cancellationsCount,
            ]);

            if ($userType === 'rider') {
                Log::info('Blocking rider', [
                    'rider_id' => $user->rider->id,
                    'previous_status' => $user->rider->global_status,
                ]);
                $previous_status = $user->rider->global_status->value;

                $user->rider->update([
                    'global_status' => RiderGlobalStatus::blocked->value,
                    'previous_global_status' => $previous_status,
                ]);
                $user->update(['blocking_reason' => 'Rider exceeded cancellation limit']);

                // Send notification to rider about account block
                $data = [
                    'title' => 'تم حظر حسابك',
                    'description' => 'لقد تجاوزت الحد المسموح به لإلغاء الرحلات. تم حظر حسابك .',
                ];

                $trip->rider->user->notify(new RiderCanceledRide($user, $data));

            } elseif ($userType === 'driver') {
                Log::info('Blocking driver', [
                    'driver_id' => $user->driver->id,
                    'previous_status' => $user->driver->global_status,
                ]);
                $previous_status = $user->driver->global_status->value;

                $user->driver->update([
                    'previous_global_status' => $previous_status,
                    'global_status' => DriverGlobalStatus::blocked->value,
                ]);
                $user->update(['blocking_reason' => 'Driver exceeded cancellation limit']);

                // Send notification to driver about account block
                $data = [
                    'title' => 'تم حظر حسابك',
                    'description' => 'لقد تجاوزت الحد المسموح به لإلغاء الرحلات. تم حظر حسابك .',
                ];
                $trip->driver->user->notify(new RiderCanceledRide($user, $data));
            }

            // broadcast(new CancellationLimitWarning($cancellationsCount, $remainingCancellations, 'You have exceeded the cancellation limit. Your account has been blocked.', $trip, $userType));
        } else {
            $warningMessage = match ($remainingCancellations) {
                2 => 'You have 2 cancellations remaining this week',
                1 => 'Warning: You have only 1 cancellation remaining this week',
                0 => 'Warning: Your next cancellation will result in a temporary block',
                default => null
            };

            if ($warningMessage) {
                Log::info('Broadcasting warning message', [
                    'trip_id' => $trip->id,
                    'remaining_cancellations' => $remainingCancellations,
                    'warning_message' => $warningMessage,
                ]);
                // broadcast(new CancellationLimitWarning($cancellationsCount, $remainingCancellations, $warningMessage, $trip, $userType));
            }
        }
    }

    public function getRemainingCancellations(User $user, string $userType): int
    {
        $weekStart = Carbon::now()->startOfWeek(Carbon::SATURDAY);
        $weekEnd = clone $weekStart;
        $weekEnd->addDays(6)->endOfDay();

        $cancellationsCount = TripCancellation::where('user_id', $user->id)
            ->where('cancelled_by', $userType)
            ->where('counts_towards_limit', true)
            ->whereBetween('cancelled_at', [$weekStart, $weekEnd])
            ->count();

        return max(0, $this->maxCancellations - $cancellationsCount);
    }

    /**
     * Edit trip destination for an ongoing trip
     *
     * @param  int  $trip_id
     *
     * @response array{data : responseData , message: string, status: integer,error : string}
     */
    public function editTripRequest(Request $request, $trip_id)
    {
        try {
            $validated = $request->validate([
                'arrival_location' => 'required|array',
                'arrival_location.latitude' => 'required|numeric',
                'arrival_location.longitude' => 'required|numeric',
                'arrival_location.address' => 'required|string',
            ]);

            // Check if user is authorized to edit this trip
            $trip = Trip::where('id', $trip_id)->first();
            $user = Auth::user();

            if (! $user->rider || $user->rider->id != $trip->rider_id) {
                return ApiResponse::error(null, 'Unauthorized: You can only edit your own trips', 403);
            }

            // Check if trip is in a valid state for editing
            $validStates = [
                TripStatus::assigned->value,
                TripStatus::driver_arriving->value,
                TripStatus::driver_arrived->value,
                TripStatus::on_trip->value,
            ];

            if (! in_array($trip->status->value, $validStates)) {
                return ApiResponse::error(null, 'Trip cannot be edited in its current state', 422);
            }

            // Prepare data for processing
            $data = [
                'trip_id' => $trip_id,
                'arrival_location' => $validated['arrival_location'],
            ];
            // Process the edit request
            $tripPriceController = app(TripPriceController::class);
            $new_data = $tripPriceController->updateTripArrival(
                $data,
                app(TripService::class),
                app(TripPricingService::class),
                app(AreaDetectionService::class)
            );

            // Check if the result is an ApiResponse (error case)
            if ($new_data instanceof \App\Http\Responses\ApiResponse) {
                // Return the error response directly
                return $new_data;
            }

            // Calculate accurate ETA
            $googleMapsService = app(GoogleMapsService::class);

            // Define locations
            $pickupLocation = [
                'lat' => $trip->tripLocation->departure_lat,
                'lng' => $trip->tripLocation->departure_lng,
            ];

            $newDropoffLocation = [
                'lat' => $validated['arrival_location']['latitude'],
                'lng' => $validated['arrival_location']['longitude'],
            ];

            // Calculate time from pickup to new dropoff
            $pickupToDropoffData = $googleMapsService->getDistanceAndTime($pickupLocation, $newDropoffLocation);
            $pickupToDropoffDuration = isset($pickupToDropoffData['duration_in_traffic_value'])
                ? $pickupToDropoffData['duration_in_traffic_value']
                : ($pickupToDropoffData['duration_value'] ?? 0);

            // Get the estimated departure time (when driver will arrive at pickup)
            $estimatedDepartureTime = Carbon::parse($trip->estimated_departure_time);

            // If estimated departure time is in the past, use actual departure time or current time
            if ($estimatedDepartureTime->isPast()) {
                if ($trip->actual_departure_time) {
                    $estimatedDepartureTime = Carbon::parse($trip->actual_departure_time);
                } else {
                    $estimatedDepartureTime = now();
                }
            }

            // Calculate ETA based on trip status
            $estimatedArrivalTime = null;

            switch ($trip->status->value) {
                case TripStatus::assigned->value:
                case TripStatus::driver_arriving->value:
                    // Driver is on the way to pickup
                    // ETA = estimated_departure_time + pickup to dropoff duration
                    $estimatedArrivalTime = $estimatedDepartureTime->copy()->addSeconds($pickupToDropoffDuration);

                    Log::info('ETA calculation for assigned/driver_arriving trip', [
                        'trip_id' => $trip->id,
                        'estimated_departure_time' => $estimatedDepartureTime->toDateTimeString(),
                        'pickup_to_dropoff_duration' => $pickupToDropoffDuration,
                        'estimated_arrival_time' => $estimatedArrivalTime->toDateTimeString(),
                    ]);
                    break;

                case TripStatus::driver_arrived->value:
                    // Driver is at pickup point
                    // ETA = now + pickup to dropoff (plus a small buffer for boarding)
                    $boardingBuffer = 180; // 3 minutes for passenger to board
                    $estimatedArrivalTime = now()->addSeconds($pickupToDropoffDuration + $boardingBuffer);

                    Log::info('ETA calculation for driver_arrived trip', [
                        'trip_id' => $trip->id,
                        'boarding_buffer' => $boardingBuffer,
                        'pickup_to_dropoff_duration' => $pickupToDropoffDuration,
                        'estimated_arrival_time' => $estimatedArrivalTime->toDateTimeString(),
                    ]);
                    break;

                case TripStatus::on_trip->value:
                    // Trip is already in progress
                    // For on_trip, use the time from pickup to new dropoff
                    // ETA = actual_departure_time + pickup to new dropoff duration

                    // Use actual departure time if available, otherwise use estimated
                    $departureTime = $trip->actual_departure_time
                        ? Carbon::parse($trip->actual_departure_time)
                        : $estimatedDepartureTime;

                    // If departure time is in the future (shouldn't happen for on_trip), use current time
                    if ($departureTime->isFuture()) {
                        $departureTime = now();
                    }

                    $estimatedArrivalTime = $departureTime->copy()->addSeconds($pickupToDropoffDuration);
                    Log::info('new data  : ', [$new_data]);
                    Log::info('ETA calculation for on_trip', [
                        'trip_id' => $trip->id,
                        'departure_time' => $departureTime->toDateTimeString(),
                        'pickup_to_dropoff_duration' => $pickupToDropoffDuration,
                        'estimated_arrival_time_without_converstion' => $estimatedArrivalTime,
                        'estimated_arrival_time' => $estimatedArrivalTime->toDateTimeString(),
                    ]);
                    break;
            }

            // Create edit request record
            $editRequestData = [
                'trip_id' => $trip->id,
                'polyline' => (string) $new_data['polyline'],
                'departure_lat' => $trip->tripLocation->departure_lat,
                'departure_lng' => $trip->tripLocation->departure_lng,
                'arrival_lat' => $validated['arrival_location']['latitude'],
                'arrival_lng' => $validated['arrival_location']['longitude'],
                'departure_address' => (string) $trip->tripLocation->departure_address,
                'arrival_address' => (string) $validated['arrival_location']['address'],
                'departure_area_id' => $trip->departure_area_id,
                'arrival_area_id' => $new_data['arrival_area_id'],
                // 'distance' => (float) $new_data['pricing_breakdown'][3]['distance'],
                'estimated_duration' => $estimatedArrivalTime,
                'previous_pricing_data' => json_encode($trip->pricing_breakdown),
                'new_pricing' => json_encode($new_data['pricing_breakdown']),
            ];

            // Extract distance from pricing breakdown - handle different structure formats
            if (isset($new_data['pricing_breakdown'][3]['distance'])) {
                // Old format with numeric index
                $editRequestData['distance'] = (float) $new_data['pricing_breakdown'][3]['distance'];
            } elseif (isset($new_data['pricing_breakdown'][$trip->vehicle_type_id]['distance'])) {
                // New format with vehicle_type_id as key
                $editRequestData['distance'] = (float) $new_data['pricing_breakdown'][$trip->vehicle_type_id]['distance'];
            } elseif (is_array($new_data['pricing_breakdown']) && ! empty($new_data['pricing_breakdown'])) {
                // Try to find distance in the first available pricing entry
                $firstKey = array_key_first($new_data['pricing_breakdown']);
                $firstPricing = $new_data['pricing_breakdown'][$firstKey];
                if (isset($firstPricing['distance'])) {
                    $editRequestData['distance'] = (float) $firstPricing['distance'];
                } else {
                    // Fallback to current trip distance if we can't find it in pricing
                    $editRequestData['distance'] = (float) $trip->distance;
                    Log::warning('Could not find distance in pricing breakdown, using current trip distance', [
                        'trip_id' => $trip->id,
                        'pricing_breakdown' => $new_data['pricing_breakdown'],
                    ]);
                }
            } else {
                // Last resort fallback
                $editRequestData['distance'] = (float) $trip->distance;
                Log::warning('Invalid pricing breakdown structure, using current trip distance', [
                    'trip_id' => $trip->id,
                    'pricing_breakdown' => $new_data['pricing_breakdown'],
                ]);
            }

            $trip->editRideRequests()
                ->updateOrCreate(
                    ['trip_id' => $trip->id],
                    $editRequestData
                );
            // Prepare response data
            $responseData = [
                'trip' => [
                    'trip_id' => $trip->id,
                    'estimated_departure_time' => $trip->estimated_departure_time,
                    'estimated_arrival_time' => $estimatedArrivalTime,
                ],
                'new_location' => [
                    'latitude' => $validated['arrival_location']['latitude'],
                    'longitude' => $validated['arrival_location']['longitude'],
                    'address' => $validated['arrival_location']['address'],
                ],
                'pricing' => array_values($new_data['pricing_breakdown'])[0],
                // 'distance' => (float) $new_data['pricing_breakdown'][3]['distance'],
                'polyline' => (string) $new_data['polyline'],
            ];
            Log::info('Edit trip request data', [
                'trip_id' => $trip->id,
                'data' => $responseData,
            ]);

            // Broadcast to driver
            broadcast(new EditTripRequest($trip, $responseData))->toOthers();

            Log::info('Edit trip request sent to driver', [
                'trip_id' => $trip->id,
                'data' => $responseData,
                'estimated_arrival_time' => $estimatedArrivalTime,
            ]);

            return ApiResponse::success(
                $responseData,
                'Trip edit request sent to driver successfully',
                200
            );
        } catch (\Exception $e) {
            Log::error('Failed to process edit trip request', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);

            return ApiResponse::error(
                null,
                'Failed to process edit trip request: '.$e->getMessage(),
                500
            );
        }
    }

    public function saveFavoriteAddress(Request $request)
    {
        $rider = Auth::user()->load('rider');

        if ($rider->type !== 'passenger') {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        $validated = $request->validate([
            'TripId' => 'required|numeric|exists:trips,id',
            'favorite_for' => ['required', Rule::in(['departure', 'arrival'])],
        ]);

        $trip = Trip::with('tripLocation')->find($validated['TripId']);

        if (! $trip || $trip->rider_id !== $rider->rider->id || ! $trip->tripLocation) {
            return ApiResponse::error(null, 'Unauthorized or invalid trip data', 404);
        }

        $lat = $validated['favorite_for'] == 'Departure'
            ? $trip->tripLocation->departure_lat
            : $trip->tripLocation->arrival_lat;

        $lng = $validated['favorite_for'] == 'Departure'
            ? $trip->tripLocation->departure_lng
            : $trip->tripLocation->arrival_lng;

        $address = $validated['favorite_for'] == 'Departure'
            ? $trip->tripLocation->departure_address
            : $trip->tripLocation->arrival_address;

        $existing = $rider->address()
            ->where('is_favorite', true)
            ->where(function ($query) use ($lat, $lng, $address) {
                $query->where('latitude', $lat)->where('longitude', $lng)->orwhere('address', $address);
            })
            ->first();

        if ($existing) {
            return ApiResponse::error(null, 'Already added as favorite', 409);
        }

        $newAddress = $rider->address()->create([
            'address' => $address,
            'latitude' => $lat,
            'longitude' => $lng,
            'is_favorite' => true,
        ]);

        if ($newAddress) {
            return ApiResponse::success($newAddress, 'Added to favorite successfully', 201, null);
        } else {
            return ApiResponse::error(null, 'Failed to add to favorite', 500);
        }
    }
}

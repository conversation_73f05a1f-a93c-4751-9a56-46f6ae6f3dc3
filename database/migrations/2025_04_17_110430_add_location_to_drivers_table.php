<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            if (DB::connection()->getDriverName() === 'pgsql') {
                DB::statement('ALTER TABLE drivers ADD COLUMN location GEOGRAPHY(POINT, 4326)');
            } else {
                $table->decimal('latitude', 10, 6)->nullable();
                $table->decimal('longitude', 10, 6)->nullable();
            }
        });
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropColumn('last_lat');
            $table->dropColumn('last_lng');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            if (DB::connection()->getDriverName() === 'pgsql') {
                DB::statement('ALTER TABLE drivers DROP COLUMN location');
            } else {
                $table->dropColumn(['latitude', 'longitude']);
            }
        });
        Schema::table('vehicles', function (Blueprint $table) {
            $table->decimal('last_lat', 10)->nullable();
            $table->decimal('last_lng', 10)->nullable();
        });
    }
};

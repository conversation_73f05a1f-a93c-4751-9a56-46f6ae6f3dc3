<?php

namespace Tests\Feature\Api;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class UserTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $this->withoutExceptionHandling();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, ['*']);
    }

    /** @test */
    public function it_gets_users_list()
    {
        $users = User::factory()
            ->count(5)
            ->create();

        $response = $this->getJson(route('api.users.index'));

        $response->assertOk()->assertSee($users[0]->name);
    }

    /** @test */
    public function it_stores_the_user()
    {
        $data = User::factory()
            ->make()
            ->toArray();

        $data['password'] = fake()->password(8);

        $response = $this->postJson(route('api.users.store'), $data);

        unset($data['password']);
        unset($data['email_verified_at']);
        unset($data['created_at']);
        unset($data['updated_at']);
        unset($data['type']);

        $this->assertDatabaseHas('users', $data);

        $response->assertStatus(201)->assertJsonFragment($data);
    }

    /** @test */
    public function it_updates_the_user()
    {
        $user = User::factory()->create();

        $data = [
            'name' => fake()->name(),
            'email' => fake()
                ->unique()
                ->safeEmail(),
        ];

        $response = $this->patchJson(route('api.users.update', $user->id), $data);

        $data['id'] = $user->id;

        $this->assertDatabaseHas('users', $data);

        $response->assertStatus(200)->assertJsonFragment($data);
    }

    /** @test */
    public function it_deletes_the_user()
    {
        $user = User::factory()->create();

        $response = $this->deleteJson(route('api.users.destroy', $user->id));

        $this->assertModelMissing($user);

        $response->assertStatus(200);
    }
}

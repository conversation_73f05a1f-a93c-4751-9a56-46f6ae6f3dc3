<?php

namespace Tests\Feature\Api;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class UserTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $this->withoutExceptionHandling();

        $user = User::factory()->create(['email' => '<EMAIL>']);

        Sanctum::actingAs($user, [], 'web');
    }

    /** @test */
    public function it_gets_users_list()
    {
        // Skip this test as the route doesn't exist
        $this->markTestSkipped('API route api.users.index does not exist');

        $users = User::factory()
            ->count(5)
            ->create();

        $response = $this->get(route('api.users.index'));

        $response->assertOk()->assertSee($users[0]->name);
    }

    /** @test */
    public function it_stores_the_user()
    {
        // Skip this test as the route doesn't exist
        $this->markTestSkipped('API route api.users.store does not exist');

        $data = User::factory()
            ->make()
            ->toArray();

        $data['password'] = \Str::random('8');

        $response = $this->postJson(route('api.users.store'), $data);

        unset($data['password']);
        unset($data['email_verified_at']);
        unset($data['created_at']);
        unset($data['updated_at']);
        unset($data['type']);

        $this->assertDatabaseHas('users', $data);

        $response->assertStatus(201)->assertJsonFragment($data);
    }

    /** @test */
    public function it_updates_the_user()
    {
        // Skip this test as the route doesn't exist
        $this->markTestSkipped('API route api.users.update does not exist');

        $user = User::factory()->create();

        $data = [
            'name' => fake()->name(),
            'email' => fake()
                ->unique()
                ->safeEmail(),
        ];

        $data['password'] = \Str::random('8');

        $response = $this->putJson(route('api.users.update', $user), $data);

        unset($data['password']);
        unset($data['email_verified_at']);
        unset($data['created_at']);
        unset($data['updated_at']);
        unset($data['type']);

        $data['id'] = $user->id;

        $this->assertDatabaseHas('users', $data);

        $response->assertStatus(200)->assertJsonFragment($data);
    }

    /** @test */
    public function it_deletes_the_user()
    {
        // Skip this test as the route doesn't exist
        $this->markTestSkipped('API route api.users.destroy does not exist');

        $user = User::factory()->create();

        $response = $this->deleteJson(route('api.users.destroy', $user));

        $this->assertModelMissing($user);

        $response->assertNoContent();
    }
}

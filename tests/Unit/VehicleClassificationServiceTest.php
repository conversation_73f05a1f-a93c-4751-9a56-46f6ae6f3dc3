<?php

namespace Tests\Unit;

use App\Enums\VehicleTypesCategories;
use App\Enums\WeightCategoryEnum;
use App\Models\Vehicle;
use App\Models\VehicleBrand;
use App\Models\VehicleClassificationRule;
use App\Models\VehicleModel;
use App\Models\VehicleType;
use App\Services\VehicleClassificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VehicleClassificationServiceTest extends TestCase
{
    use RefreshDatabase;

    private VehicleClassificationService $service;

    private VehicleBrand $brand;

    private VehicleModel $model;

    private VehicleType $economyType;

    private VehicleType $comfortType;

    private VehicleType $luxuryType;

    private VehicleType $lightCoveredType;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new VehicleClassificationService;

        // Create test data
        $this->brand = VehicleBrand::factory()->create(['name_en' => 'Toyota']);
        $this->model = VehicleModel::factory()->create([
            'vehicle_brand_id' => $this->brand->id,
            'name_en' => 'Camry',
        ]);

        // Create vehicle types
        $this->economyType = VehicleType::factory()->create([
            'name_en' => 'Economy',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $this->comfortType = VehicleType::factory()->create([
            'name_en' => 'Comfort',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $this->luxuryType = VehicleType::factory()->create([
            'name_en' => 'Luxury',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $this->lightCoveredType = VehicleType::factory()->create([
            'name_en' => 'Light Covered Truck',
            'category' => VehicleTypesCategories::Freight,
            'is_covered' => true,
            'weight_category' => WeightCategoryEnum::LessThan1000kg,
            'status' => true,
        ]);
    }

    /** @test */
    public function it_classifies_passenger_vehicle_correctly()
    {
        // Create classification rule for Comfort type
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $rule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        // Create vehicle that matches the rule
        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $this->economyType->id, // Start with economy
            'year' => 2020,
            'seat_number' => 4,
        ]);

        $result = $this->service->classifyVehicle($vehicle);

        $this->assertEquals($this->comfortType->id, $result);
    }

    /** @test */
    public function it_returns_null_for_unmatched_passenger_vehicle()
    {
        // Create vehicle that doesn't match any rules
        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2015, // Too old
            'seat_number' => 4,
        ]);

        $result = $this->service->classifyVehicle($vehicle);

        // Service should return null when no classification rules match
        $this->assertNull($result);
    }

    /** @test */
    public function it_classifies_freight_vehicle_correctly()
    {
        // Create classification rule for Light Covered Truck
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->lightCoveredType->id,
            'category' => VehicleTypesCategories::Freight,
        ]);

        $rule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2015,
            'max_year' => 2025,
            'is_covered' => true,
            'weight_category' => WeightCategoryEnum::LessThan1000kg->value,
        ]);

        // Create freight vehicle that matches the rule
        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $this->lightCoveredType->id,
            'year' => 2020,
        ]);

        // Debug the vehicle type properties
        $vehicle->load('vehicleType');
        dump([
            'vehicle_type_id' => $vehicle->vehicle_type_id,
            'is_covered' => $vehicle->vehicleType->is_covered ?? 'null',
            'weight_category' => $vehicle->vehicleType->weight_category?->value ?? 'null',
            'expected_weight' => 'less_than_1000kg',
        ]);

        $result = $this->service->classifyVehicle($vehicle);

        echo "\nClassification result: ".($result ?? 'null')."\n";
        echo 'Expected: '.$this->lightCoveredType->id."\n";

        $this->assertEquals($this->lightCoveredType->id, $result);
    }

    /** @test */
    public function it_returns_null_for_vehicle_without_model_or_brand()
    {
        // Create a vehicle with a model but test the service with null model
        $vehicle = Vehicle::factory()->create([
            'vehicle_type_id' => $this->economyType->id,
        ]);

        // Manually set the vehicle_model_id to null to test the service
        $vehicle->vehicle_model_id = null;

        $result = $this->service->classifyVehicle($vehicle);

        $this->assertNull($result);
    }

    /** @test */
    public function it_auto_classifies_and_updates_vehicle()
    {
        // Create classification rule
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->luxuryType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $rule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2020,
            'max_year' => 2024,
            'seat_numbers' => [4],
        ]);

        // Create vehicle with different type
        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2022,
            'seat_number' => 4,
        ]);

        $result = $this->service->autoClassifyVehicle($vehicle);

        $this->assertTrue($result);
        $this->assertEquals($this->luxuryType->id, $vehicle->fresh()->vehicle_type_id);
    }

    /** @test */
    public function it_gets_suggested_vehicle_types_with_classification_first()
    {
        // Create classification rule for Comfort type
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $rule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4],
        ]);

        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2020,
            'seat_number' => 4,
        ]);

        $options = $this->service->getSuggestedVehicleTypes($vehicle);

        // Check that suggested type is first and marked with star
        $firstKey = array_key_first($options);
        $this->assertEquals($this->comfortType->id, $firstKey);
        $this->assertStringContainsString('⭐', $options[$firstKey]);
        $this->assertStringContainsString('Suggested', $options[$firstKey]);
    }

    /** @test */
    public function it_handles_multiple_qualifications_per_rule()
    {
        // Create rule with multiple qualifications
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->luxuryType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        // First qualification: Toyota Camry 2020-2024
        $rule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2020,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        // Second qualification: Any brand/model 2022-2024 with 6 seats
        $rule->qualifications()->create([
            'min_year' => 2022,
            'max_year' => 2024,
            'seat_numbers' => [6],
        ]);

        // Test vehicle that matches second qualification
        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2023,
            'seat_number' => 6,
        ]);

        $result = $this->service->classifyVehicle($vehicle);

        $this->assertEquals($this->luxuryType->id, $result);
    }
}

<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ConfigController;
use App\Http\Controllers\Api\DriverController;
use App\Http\Controllers\Api\DriversTripController;
use App\Http\Controllers\Api\DriversVehicleController;
use App\Http\Controllers\Api\RiderController;
use App\Http\Controllers\Api\RidersTripController;
use App\Http\Controllers\Api\TripController;
use App\Http\Controllers\Api\TripRatingController;
use App\Http\Controllers\Api\TripsTripRatingController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\UsersRiderController;
use Illuminate\Support\Facades\Route;

Route::name('api.')
    ->prefix('api')
    ->group(function () {

        Route::post('/login', [AuthController::class, 'login'])->name(
            'login'
        );

        Route::post('/otp', [AuthController::class, 'otp'])->name(
            'api.otp'
        );

        Route::get('/config', [ConfigController::class, 'configuration']);

        Route::post('refresh-token', [AuthController::class, 'refresh'])
            ->name('api.refresh');

        Route::middleware('auth:sanctum')->group(function () {

            Route::post('users/logout', [AuthController::class, 'logout'])
                ->name('api.logout');

            Route::get('/me', [UserController::class, 'me'])->name('api.me');

            Route::post('users/verify email', [AuthController::class, 'verify_email'])->name(
                'user.verify email');

            Route::patch('users/fcm-token', [AuthController::class, 'register_fcm'])
                ->name('fcm-token');

            Route::get('/trips/{trip}/trip-ratings', [
                TripsTripRatingController::class,
                'index',
            ])->name('trips.trip-ratings.index');

            Route::post('/trips/{trip}/trip-ratings', [
                TripsTripRatingController::class,
                'store',
            ])->name('trips.trip-ratings.store');

            Route::get('/trips', [TripController::class, 'index'])->name(
                'trips.index'
            );

            Route::post('/trips', [TripController::class, 'store'])->name(
                'trips.store'
            );

            Route::patch('/trips/{id}', [TripController::class, 'update'])
                ->name('trips.request.update');

            Route::patch('/trips/edit/{trip_id}', [RidersTripController::class, 'editTripRequest'])
                ->name('trips.active.edit');

            Route::get('/trip/{id}', [TripController::class, 'Tripstatus'])->name('trips.status');

            Route::post('trips/cancel-driver-search/{id}', [TripController::class, 'cancelDriverSearch']);
            Route::get('/trips/{trip}', [TripController::class, 'show'])->name(
                'trips.show'
            );

            Route::post('rider/trip/add-favorite', [
                RidersTripController::class,
                'addToFavorite',
            ])->name('trip.add-favorite');

            Route::post('rider/address/set-shortcut/{addressId}', [
                RiderController::class,
                'setAddressShortcut',
            ])->name('address.set-shortcut');

            Route::post('/trips/confirm/{id}', [TripController::class, 'confirmTrip'])
                ->name('vehicle.types');

            Route::get('rider/home', [RiderController::class, 'home'])
                ->name('rider.home');

            Route::get('rider/share-location/{tripId}', [UsersRiderController::class, 'shareLocation'])
                ->name('rider.share-location');

            Route::get('rider/my-trips', [
                RidersTripController::class,
                'tripList',
            ])->name('rider.trips.list');

            Route::get('rider/favorite/addresses', [RidersTripController::class, 'favoriteAddresses'])
                ->name('rider.favorite.addresses');

            Route::get('rider/favorite/trips', [RidersTripController::class, 'favoriteTrips'])
                ->name('rider.favorite.trips');

            Route::get('rider/trip/{trip}', [
                RidersTripController::class,
                'trip',
            ])->name('rider.trip.details');

            Route::post('rider/preferences', [RiderController::class, 'preferences'])
                ->name('rider.preferences');

            Route::get('driver/my-trips', [
                DriversTripController::class,
                'tripList',
            ])->name('drivers.trips.list');

            Route::get('driver/trip/{tripId}', [
                DriversTripController::class,
                'trip',
            ])->name('driver.trip.details');

            Route::post('driver/trip-ratings', [
                DriversTripController::class,
                'driverReview',
            ])->name('driver.trip-ratings');

            Route::get('/driver/profile', [DriverController::class, 'profile']);

            Route::patch('/driver/location', [DriverController::class, 'updateLocationHttp'])
                ->name('driver.location.update');

            Route::post('driver/documents', [DriverController::class, 'uploadDocuments'])
                ->name('driver.documents');

            Route::get('driver/vehicles/list', [DriversVehicleController::class, 'ListVehicles'])
                ->name('drivers.vehicles.list');

            Route::get('driver/vehicle/{vehicleId}', [DriversVehicleController::class, 'vehicleDetails'])
                ->name('drivers.vehicles.detials');

            Route::post('/driver/vehicles', [DriverController::class, 'addVehicle'])
                ->name('drivers.vehicle.create');

            Route::delete('/driver/vehicle/{vehicleId}', [DriversVehicleController::class, 'DeleteVehicle'])
                ->name('drivers.vehicle.delete');

            Route::post('/driver/vehicle/{vehicle?}', [DriverController::class, 'UpdateVehicle'])
                ->name('drivers.vehicle.update');
            Route::post('rider/favorite-addresses', [RiderController::class, 'addFavoriteAddress'])
                ->name('rider.favorite-addresses.add');
            Route::post('rider/addresses/label', [RiderController::class, 'updateLabel'])
                ->name('rider.favorite-addresses.update');
            Route::delete('rider/addresses/label/{addressId}', [RiderController::class, 'removeLabel'])
                ->name('rider.favorite-addresses.label.remove');

            Route::post('rider/trip/favorite-addresses', [RidersTripController::class, 'saveFavoriteAddress'])
                ->name('rider.favorite-addresses.save');

            Route::post('upload-picture', [UserController::class, 'picture'])->name(
                'user.picture'
            );

            Route::post('user/status', [UserController::class, 'status'])->name(
                'user.update.status'
            );

            Route::post('rider/trip-ratings', [TripRatingController::class, 'riderReview'])->name('rider.trip-ratings');
            Route::post('/rider/heartbeat', [RiderController::class, 'heartbeat'])
                ->name('rider.heartbeat');

            Route::patch('users/profile', [UserController::class, 'patch'])->name(
                'user.profile');

            // New API routes uncommented
            Route::get('/drivers', [DriverController::class, 'index'])->name('drivers.index');
            Route::post('/drivers', [DriverController::class, 'store'])->name('drivers.store');
            Route::patch('/drivers/{id}', [DriverController::class, 'update'])->name('drivers.update');
            Route::delete('/drivers/{id}', [DriverController::class, 'destroy'])->name('drivers.destroy');

            // Additional missing API routes
            Route::get('/drivers/{id}/trips', [DriversTripController::class, 'driverTrips'])->name('drivers.trips');
            Route::get('/drivers/{id}/vehicles', [DriversVehicleController::class, 'driverVehicles'])->name('drivers.vehicles');

        });
    });
